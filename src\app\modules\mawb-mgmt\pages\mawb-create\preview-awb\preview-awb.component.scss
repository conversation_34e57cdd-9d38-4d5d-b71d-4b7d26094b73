// Optimized SCSS - Reduced from 2267 lines to minimal essential styles
$border: 1px solid #999;
$white: #fff;
$grey: #f2f2f2;
$text: #333;

// Base placeholders to reduce CSS output
%abs {
	position: absolute;
}
%rel {
	position: relative;
}
%border {
	background: $white;
	border: $border;
}
%border-grey {
	background: $grey;
	border: $border;
}
%text {
	font: 400 12px/14px sans-serif;
	color: $text;
	letter-spacing: 0;
	white-space: nowrap;
}
%text-14 {
	font: 400 14px/14px sans-serif;
	color: $text;
	letter-spacing: 0;
}
%text-center {
	text-align: center;
	white-space: nowrap;
}

// Other charges list component
.other-charges-list {
	height: 170px;
	margin-top: 40px;
	overflow-y: auto;
	border: 1px solid var(--iata-grey-100);
	border-radius: 6px;

	> .row {
		width: 100%;
		padding: 12px;
		border-bottom: 1px solid var(--iata-grey-100);
	}

	.label-name {
		margin-right: 4px;
	}
	.col-charge_payment_type {
		width: 240px;
		flex: 0 0 240px;
	}
	.col-entitlement {
		width: 140px;
		flex: 0 0 140px;
	}
	.col-delete {
		width: 60px;
		flex: 0 0 60px;
		display: inline-flex;
		align-items: center;
	}
	.cell-content {
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

// Main AWB container with all nested styles
.element-proview-AWB {
	@extend %text-14;
	@extend %border;
	display: flex;
	flex-direction: row;
	justify-content: center;
	width: 100%;

	// All groups, overlaps, rectangles, and text wrappers use common patterns
	.group-wrapper {
		@extend %border;
		width: 1520px;
		height: 2590px;
	}
	.group {
		@extend %rel;
		height: 2590px;
		@extend %border;
		box-shadow:
			0px 8px 18px #00000014,
			0px 4px 4px #0000000a;
	}

	.button-footer {
		@extend %rel;
		display: flex;
		justify-content: flex-end;
		margin: 80px 80px 0 0;
		.close-button {
			margin-right: 20px;
		}
	}

	.overlap-wrapper {
		@extend %abs;
		top: 54px;
		left: 113px;
		width: 1323px;
		height: 2286px;
	}
	.overlap {
		@extend %rel;
		height: 2286px;
	}

	// Common elements using placeholders
	.div,
	.group-2 {
		@extend %abs;
		@extend %border;
		width: 1px;
		height: 54px;
		top: 0;
	}
	.div {
		left: 73px;
	}
	.group-2 {
		left: 145px;
	}

	// All overlap groups use same pattern
	.overlap-group,
	.overlap-group-2,
	.overlap-group-3,
	.overlap-4,
	.overlap-5 {
		@extend %rel;
		@extend %border;
	}
	.overlap-group {
		height: 216px;
	}
	.overlap-group-2 {
		width: 331px;
		height: 72px;
	}
	.overlap-group-3 {
		width: 662px;
		height: 162px;
	}
	.overlap-4 {
		width: 662px;
		height: 215px;
	}
	.overlap-5 {
		width: 820px;
		height: 214px;
	}

	// All rectangles use same base pattern
	.rectangle,
	[class*='rectangle-'] {
		@extend %abs;
		@extend %border;
	}
	.rectangle {
		width: 662px;
		height: 216px;
		top: 0;
		left: 0;
	}

	// All text wrappers use same base pattern
	[class*='text-wrapper'] {
		@extend %abs;
		@extend %text;
		height: 14px;
	}
	.text-wrapper {
		top: 16px;
		left: 16px;
	}
	.text-wrapper-2 {
		top: 15px;
		left: 40px;
	}
	.text-wrapper-3 {
		top: 15px;
		left: 15px;
	}
	.text-wrapper-4 {
		top: 72px;
		left: 15px;
		font-weight: 500;
		font-size: 20px;
	}
	.text-wrapper-5 {
		top: 129px;
		left: 15px;
		font-size: 14px;
	}

	// All groups use absolute positioning
	[class*='group-'] {
		@extend %abs;
	}
	.group-3 {
		top: 268px;
		left: 0;
		width: 664px;
		height: 216px;
	}
	.group-4 {
		top: 53px;
		left: 0;
		width: 1323px;
		height: 2233px;
	}
	.group-5 {
		top: 0;
		left: 661px;
		width: 668px;
		height: 162px;
	}
	.group-6 {
		top: 430px;
		left: 0;
		width: 664px;
		height: 144px;
	}
	.group-7 {
		top: 857px;
		left: 0;
		width: 1325px;
		height: 144px;
	}
	.group-8 {
		top: 430px;
		left: 661px;
		width: 664px;
		height: 215px;
	}
	.group-9 {
		top: 1573px;
		left: 503px;
		width: 822px;
		height: 214px;
	}
	.group-10 {
		top: 1786px;
		left: 503px;
		width: 822px;
		height: 214px;
	}

	// Content wrappers
	.overlap-group-wrapper {
		@extend %abs;
		width: 664px;
		height: 216px;
		top: 53px;
		left: 0;
	}
	.div-wrapper {
		@extend %abs;
		width: 333px;
		height: 72px;
		top: 0;
		left: 331px;
	}

	.party-content-wrapper {
		@extend %abs;
		height: auto;
		top: 90px;
		left: 24px;
		right: 24px;
		line-height: 20px;
		word-break: break-word;
		white-space: normal;
		&.carrier-agent {
			top: 60px;
		}
	}

	.item-wrapper {
		@extend %abs;
		height: auto;
		top: 40px;
		left: 16px;
		right: 16px;
		line-height: 14px;
		word-break: break-word;
		white-space: normal;

		&.center {
			text-align: center;
		}
		&.shipper-consignee {
			left: 40px;
		}
		&.charge-code {
			left: 8px;
			right: 0;
		}
		&.charge-weight {
			top: 30px;
			text-align: center;
		}
		&.weight-unit {
			top: 30px;
			left: 4px;
		}
		&.rate-class {
			top: 102px;
			left: 5px;
		}
		&.signature {
			top: 0;
			margin-top: -15px;
			text-align: center;
			&.date {
				margin-left: 120px;
				text-align: left;
			}
			&.at-place {
				margin-right: 60px;
			}
			&.carrier {
				margin-right: 150px;
				text-align: right;
			}
		}
	}

	// Special wrappers
	.consignee-s-name-and-wrapper {
		@extend %rel;
		width: 331px;
		height: 72px;
		@extend %border-grey;
	}
	.issuing-carrier-s-wrapper {
		@extend %rel;
		width: 662px;
		height: 144px;
		@extend %border;
	}
	.it-is-agreed-that-wrapper {
		@extend %rel;
		width: 662px;
		height: 217px;
		@extend %border;
	}
	.CHGS-code-wrapper {
		@extend %rel;
		width: 36px;
		height: 72px;
		@extend %border;
	}
	.KG-lb-wrapper {
		@extend %rel;
		width: 18px;
		height: 72px;
		@extend %border;
	}
	.INSURANCE-if-carrier-wrapper {
		@extend %rel;
		width: 465px;
		height: 72px;
		@extend %border;
	}

	// Overlap patterns
	.overlap-2 {
		@extend %rel;
		width: 1329px;
		height: 2233px;
	}
	.overlap-3 {
		@extend %rel;
		width: 1323px;
		height: 144px;
		@extend %border;
	}

	// Common text elements
	.p {
		@extend %abs;
		@extend %text;
		height: 14px;
		top: 29px;
		left: 186px;
	}
	.vector {
		@extend %abs;
		width: 553px;
		height: 2px;
		top: 0;
		left: 0;
		object-fit: cover;
	}
	.vector-2 {
		@extend %abs;
		width: 818px;
		height: 1px;
		top: 0;
		left: 0;
	}

	.shipper-certifies {
		@extend %abs;
		@extend %text;
		width: 553px;
		height: 42px;
		top: 50px;
		left: 129px;
	}

	.it-is-agreed-that {
		@extend %abs;
		@extend %text;
		width: 547px;
		height: 126px;
		top: 45px;
		left: 57px;
		letter-spacing: 0.24px;
	}

	.span {
		@extend %text;
	}
	.text-wrapper-6 {
		font-weight: 500;
	}

	// Special text elements
	.chargeable-weight {
		@extend %abs;
		@extend %text;
		@extend %text-center;
		height: 28px;
		top: 22px;
		left: 32px;
	}

	.nature-and-quantity {
		@extend %abs;
		@extend %text;
		@extend %text-center;
		height: 28px;
		top: 16px;
		left: 85px;
	}

	.KG-lb {
		@extend %abs;
		color: $text;
		font-size: 8px;
		height: 20px;
		top: 25px;
		left: 3px;
		line-height: normal;
	}

	.CHGS-code {
		@extend %abs;
		@extend %text;
		@extend %text-center;
		height: 28px;
		top: 8px;
		left: 0;
	}

	.INSURANCE-if-carrier {
		@extend %abs;
		@extend %text;
		width: 419px;
		height: 42px;
		top: 15px;
		left: 15px;
	}

	.for-carrier-s-use {
		@extend %abs;
		@extend %text-14;
		@extend %text-center;
		height: 48px;
		top: 12px;
		left: 58px;
		line-height: 24px;
	}

	// Remaining specific positioning - consolidated common patterns
	.overlap-6 {
		@extend %rel;
		width: 662px;
		height: 54px;
		@extend %border;
	}
	.overlap-7 {
		@extend %rel;
		width: 332px;
		height: 72px;
		@extend %border;
	}
	.overlap-8 {
		@extend %rel;
		width: 662px;
		height: 72px;
		@extend %border;
	}
	.overlap-9 {
		@extend %rel;
		width: 74px;
		height: 574px;
	}
	.overlap-10 {
		@extend %rel;
		width: 20px;
		height: 574px;
	}
	.overlap-11 {
		@extend %rel;
		width: 145px;
		height: 574px;
	}
	.overlap-12 {
		@extend %rel;
		width: 126px;
		height: 538px;
	}
	.overlap-13 {
		@extend %rel;
		width: 128px;
		height: 574px;
	}
	.overlap-14 {
		@extend %rel;
		width: 57px;
		height: 72px;
		@extend %border;
	}
	.overlap-15 {
		@extend %rel;
		width: 75px;
		height: 72px;
		@extend %border;
	}
	.overlap-16 {
		@extend %rel;
		width: 72px;
		height: 72px;
	}
	.overlap-17 {
		@extend %rel;
		width: 504px;
		height: 72px;
	}
	.overlap-18 {
		@extend %rel;
		height: 72px;
	}
	.overlap-19 {
		@extend %rel;
		width: 756px;
		height: 92px;
	}
	.overlap-20 {
		@extend %rel;
		width: 217px;
		height: 72px;
		@extend %border;
	}
	.overlap-21 {
		@extend %rel;
		width: 208px;
		height: 72px;
		@extend %border;
	}
	.overlap-22 {
		@extend %rel;
		width: 207px;
		height: 72px;
		@extend %border;
	}
	.overlap-23 {
		@extend %rel;
		width: 166px;
		height: 72px;
		@extend %border;
	}
	.overlap-24 {
		@extend %rel;
		width: 198px;
		height: 72px;
		@extend %border;
	}
	.overlap-25 {
		@extend %rel;
		width: 62px;
		height: 72px;
		@extend %border;
	}
	.overlap-26 {
		@extend %rel;
		width: 239px;
		height: 72px;
		@extend %border;
	}
	.overlap-27 {
		@extend %rel;
		width: 303px;
		height: 72px;
		@extend %border;
	}

	// Group positioning patterns
	.group-11 {
		top: 141px;
		left: 129px;
		width: 555px;
		height: 43px;
	}
	.group-12 {
		top: 1999px;
		left: 503px;
		width: 820px;
		height: 143px;
		@extend %border;
	}
	.group-13 {
		@extend %rel;
		width: 824px;
		height: 42px;
		top: 70px;
	}
	.group-14 {
		top: 214px;
		left: 661px;
		width: 664px;
		height: 217px;
	}
	.group-15 {
		width: 664px;
		top: 161px;
		left: 661px;
		height: 54px;
	}
	.group-16 {
		top: 573px;
		left: 0;
		width: 334px;
		height: 72px;
	}
	.group-17 {
		top: 644px;
		left: 0;
		width: 664px;
		height: 72px;
	}
	.group-18 {
		top: 715px;
		left: 0;
		width: 74px;
		height: 72px;
	}

	// Rectangle positioning - using common dimensions
	.rectangle-2 {
		top: 0;
		left: 0;
		width: 126px;
		height: 36px;
	}
	.rectangle-3 {
		width: 126px;
		height: 503px;
		top: 35px;
		left: 0;
	}
	.rectangle-4 {
		top: 0;
		left: 0;
		width: 126px;
		height: 72px;
	}
	.rectangle-5 {
		width: 126px;
		height: 503px;
		top: 71px;
		left: 0;
	}
	.rectangle-6 {
		top: 0;
		left: 0;
		width: 144px;
		height: 72px;
	}
	.rectangle-7 {
		width: 144px;
		height: 503px;
		top: 71px;
		left: 0;
	}
	.rectangle-8 {
		top: 0;
		left: 0;
		width: 208px;
		height: 72px;
	}
	.rectangle-9 {
		width: 208px;
		height: 432px;
		top: 71px;
		left: 0;
	}
	.rectangle-10 {
		width: 208px;
		height: 72px;
		top: 502px;
		left: 0;
	}
	.rectangle-11 {
		top: 0;
		left: 0;
		width: 408px;
		height: 60px;
	}
	.rectangle-12 {
		width: 408px;
		height: 515px;
		top: 59px;
		left: 0;
	}

	// Text positioning - common patterns
	.text-wrapper-7 {
		top: 28px;
		left: 125px;
	}
	.text-wrapper-8 {
		top: 28px;
		left: 356px;
	}
	.text-wrapper-9 {
		top: 28px;
		left: 544px;
	}
	.text-wrapper-10 {
		top: 19px;
		left: 15px;
	}
	.text-wrapper-11 {
		width: 37px;
		height: 42px;
		top: 14px;
		left: 17px;
		@extend %text-center;
	}
	.text-wrapper-12 {
		top: 9px;
		left: 17px;
	}
	.text-wrapper-13 {
		top: 11px;
		left: 7px;
	}
	.text-wrapper-14 {
		top: 29px;
		left: 38px;
		@extend %text-center;
	}
	.text-wrapper-15 {
		top: 29px;
		left: 91px;
		@extend %text-center;
	}
	.text-wrapper-16 {
		width: 42px;
		height: 28px;
		top: 21px;
		left: 41px;
		@extend %text-center;
	}

	// Additional groups with specific positioning
	.group-19 {
		top: 1000px;
		left: 0;
		width: 72px;
		height: 574px;
	}
	.group-20 {
		top: 0;
		left: 0;
		width: 74px;
		height: 72px;
	}
	.group-21 {
		top: 71px;
		left: 0;
		width: 72px;
		height: 432px;
		@extend %border;
	}
	.group-22 {
		top: 502px;
		left: 0;
		width: 72px;
		height: 72px;
		@extend %border;
	}
	.group-23 {
		top: 1000px;
		left: 196px;
		width: 18px;
		height: 574px;
	}
	.group-24 {
		width: 20px;
		height: 72px;
		top: 0;
		left: 0;
	}
	.group-25 {
		top: 71px;
		left: 0;
		width: 18px;
		height: 503px;
		@extend %border;
	}
	.group-26 {
		top: 1000px;
		left: 213px;
		width: 18px;
		height: 574px;
		@extend %border-grey;
	}
	.group-27 {
		top: 1000px;
		left: 372px;
		width: 18px;
		height: 574px;
		@extend %border-grey;
	}
	.group-28 {
		top: 1000px;
		left: 514px;
		width: 18px;
		height: 574px;
		@extend %border-grey;
	}
	.group-29 {
		top: 1000px;
		left: 674px;
		width: 18px;
		height: 574px;
		@extend %border-grey;
	}
	.group-30 {
		top: 1000px;
		left: 898px;
		width: 18px;
		height: 574px;
		@extend %border-grey;
	}

	// Overlap group patterns
	.overlap-group-4 {
		@extend %rel;
		width: 72px;
		height: 72px;
		@extend %border;
	}
	.overlap-group-5 {
		@extend %rel;
		width: 143px;
		height: 574px;
		background-size: 100% 100%;
	}
	.overlap-group-6 {
		@extend %rel;
		width: 126px;
		height: 574px;
	}
	.overlap-group-7 {
		@extend %rel;
		width: 144px;
		height: 574px;
	}
	.overlap-group-8 {
		@extend %rel;
		width: 208px;
		height: 574px;
	}
	.overlap-group-9 {
		@extend %rel;
		width: 408px;
		height: 574px;
	}
	.overlap-group-10 {
		@extend %rel;
		width: 126px;
		height: 72px;
		@extend %border;
	}
	.overlap-group-11 {
		width: 166px;
		height: 27px;
		top: 0;
		left: 165px;
		background-size: 100% 100%;
	}
}

// PDF Export Styles
.export-button {
	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
	mat-icon {
		margin-right: 8px;
	}
}

// Print styles
@media print {
	.element-proview-AWB {
		.button-footer {
			display: none !important;
		}
		.overlap-wrapper {
			box-shadow: none !important;
			border: none !important;
		}
	}
}
